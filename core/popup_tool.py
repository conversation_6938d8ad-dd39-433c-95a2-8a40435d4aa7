"""
弹窗处理工具 - 简化版本
基于UIAutomator2的弹窗检测和处理工具，提供简洁易用的API
参考tools/pop_example.py实现，适配项目代码风格
"""
import time
import uiautomator2 as u2
from typing import Optional, List, Dict, Any, Callable
from core.logger import log


class PopupTool:
    """弹窗处理工具类 - 简化版本"""

    def __init__(self, device: u2.Device = None):
        """
        初始化弹窗处理工具
        
        Args:
            device: UIAutomator2设备对象，如果为None则使用默认连接
        """
        if device is None:
            self.device = u2.connect()
        else:
            self.device = device

        # 常见的关闭按钮文本
        self.close_texts = [
            '关闭', '取消', '确定', '同意', '知道了', '我知道了',
            '立即体验', '稍后再说', '跳过', '继续', '允许', '拒绝',
            '好的', '明白', '下次再说', '暂不需要', '以后再说',
            'X', '×', 'Close', 'OK', 'Cancel', 'Skip',
            'Allow', 'Deny', 'Later', 'Continue', 'Got it',
            'Dismiss', 'Accept', 'Decline', 'Not now'
        ]

        # 常见的关闭按钮resource-id - 扩展版本
        self.close_ids = [
            'com.transsion.aivoiceassistant:id/close_iv',
            'com.facemoji.lite.transsion:id/agree_button', # 文档总结 同意按钮resource-id,
            'com.transsion.screenrecorder:id/btn_positive', # 开始录屏时的确定按钮resource-id,
            'com.android.permissioncontroller:id/permission_allow_foreground_only_button', # 录屏权限弹窗,
            'com.facemoji.lite.transsion:id/dialog_ok', # 键盘切换弹窗,
            'com.android.permissioncontroller:id/permission_allow_button', # 日程弹窗按钮
            'android:id/button1',  # 系统对话框确定按钮
            'android:id/button2',  # 系统对话框取消按钮
            'android:id/button3',  # 系统对话框第三个按钮
            'com.android.packageinstaller:id/permission_allow_button',
            'com.android.packageinstaller:id/permission_deny_button',
            'android:id/aerr_close',  # ANR对话框关闭按钮
            'android:id/aerr_restart',  # ANR对话框重启按钮
            'android:id/closeButton',
            'android:id/dismiss',
            'android:id/cancel',
            'android:id/ok',
            'android:id/positive',
            'android:id/negative',
            'android:id/neutral',
            'com.android.systemui:id/dismiss_button',
            'com.google.android.gms:id/cancel',
            'com.google.android.gms:id/dismiss',
        ]

        # 弹窗类名 - 扩展版本
        self.popup_classes = [
            'android.app.Dialog',
            'android.app.AlertDialog',
            'android.widget.PopupWindow',
            'android.support.v7.app.AlertDialog',
            'androidx.appcompat.app.AlertDialog',
            'android.app.ProgressDialog',
            'android.widget.Toast',
            'android.view.ViewGroup',  # 自定义弹窗容器
            'android.widget.FrameLayout',  # 覆盖层
            'android.widget.LinearLayout',  # 弹窗布局
            'android.widget.RelativeLayout',  # 弹窗布局
            'androidx.fragment.app.DialogFragment',
            'com.google.android.material.dialog.MaterialAlertDialog',
        ]

        # 弹窗特征关键词
        self.popup_keywords = [
            '弹窗', '对话框', '提示', '警告', '确认', '选择',
            'dialog', 'popup', 'alert', 'modal', 'overlay',
            '权限', '许可', '允许', '拒绝', '同意', '取消',
            '更新', '升级', '下载', '安装', '卸载',
            '登录', '注册', '验证', '输入', '设置'
        ]

    def detect_and_close_popup(self, timeout: int = 5, check_interval: float = 0.5, debug_mode: bool = False, wait_for_popup: bool = False) -> bool:
        """
        检测并关闭弹窗 - 增强版本

        Args:
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
            debug_mode: 是否启用调试模式
            wait_for_popup: 是否等待弹窗出现（False时如果没有弹窗立即返回）

        Returns:
            bool: 是否成功处理弹窗
        """
        start_time = time.time()
        log.info(f"开始检测弹窗，超时时间: {timeout}秒，调试模式: {debug_mode}，等待模式: {wait_for_popup}")

        # 如果启用调试模式，先输出当前屏幕状态
        if debug_mode:
            debug_info = self.debug_current_screen()
            log.info(f"当前屏幕调试信息: 可点击元素数量={debug_info.get('clickable_elements', {}).get('total_count', 0)}")

        # 首先进行一次快速检测
        initial_popup_detected = self.is_popup_present(fast_mode=not wait_for_popup)
        log.debug(f"初始弹窗检测结果: {initial_popup_detected}")

        # 如果不等待弹窗且初始检测没有弹窗，直接返回
        if not wait_for_popup and not initial_popup_detected:
            log.info("未检测到弹窗且不等待弹窗出现，直接返回")
            return False

        attempt_count = 0
        consecutive_no_popup_count = 0  # 连续未检测到弹窗的次数
        max_consecutive_no_popup = 3    # 最大连续未检测到弹窗次数（避免无限等待）

        while time.time() - start_time < timeout:
            try:
                attempt_count += 1
                log.debug(f"弹窗检测尝试 #{attempt_count}")

                # 检查是否真的有弹窗（使用快速模式如果不等待弹窗）
                popup_detected = self.is_popup_present(fast_mode=not wait_for_popup)
                log.debug(f"弹窗检测结果: {popup_detected}")

                if not popup_detected:
                    consecutive_no_popup_count += 1
                    log.debug(f"未检测到弹窗，连续次数: {consecutive_no_popup_count}")

                    # 如果不等待弹窗且连续多次未检测到，提前退出
                    if not wait_for_popup and consecutive_no_popup_count >= max_consecutive_no_popup:
                        log.info(f"连续 {consecutive_no_popup_count} 次未检测到弹窗，提前退出")
                        return False

                    time.sleep(check_interval)
                    continue
                else:
                    # 重置连续未检测到的计数
                    consecutive_no_popup_count = 0

                # 1. 首先尝试通过UI层次结构查找
                log.debug("尝试通过UI层次结构关闭弹窗")
                if self._try_close_by_ui_hierarchy():
                    log.info("通过UI层次结构成功关闭弹窗")
                    # 使用改进的验证方法
                    if self._verify_popup_closed():
                        log.info("确认弹窗已关闭")
                        return True
                    else:
                        log.warning("弹窗似乎未完全关闭，继续尝试")

                # 2. 尝试通过坐标位置查找
                log.debug("尝试通过位置检测关闭弹窗")
                if self._try_close_by_position():
                    log.info("通过位置检测成功关闭弹窗")
                    if self._verify_popup_closed():
                        log.info("确认弹窗已关闭")
                        return True
                    else:
                        log.warning("弹窗似乎未完全关闭，继续尝试")

                # 3. 尝试返回键
                log.debug("尝试通过返回键关闭弹窗")
                if self._try_back_key():
                    log.info("通过返回键成功关闭弹窗")
                    if self._verify_popup_closed():
                        log.info("确认弹窗已关闭")
                        return True
                    else:
                        log.warning("返回键未能关闭弹窗")

                # 如果调试模式且多次尝试失败，输出详细信息
                if debug_mode and attempt_count % 5 == 0:
                    log.warning(f"已尝试 {attempt_count} 次，弹窗仍存在")
                    debug_info = self.debug_current_screen()
                    log.info(f"当前屏幕状态: {debug_info}")

                time.sleep(check_interval)

            except Exception as e:
                log.warning(f"弹窗检测过程中出现异常: {e}")
                if debug_mode:
                    import traceback
                    log.error(f"异常详情: {traceback.format_exc()}")
                time.sleep(check_interval)

        log.warning(f"弹窗检测超时，尝试了 {attempt_count} 次，未能成功关闭弹窗")

        # 超时后的最终调试信息
        if debug_mode:
            final_debug = self.debug_current_screen()
            log.error(f"超时时的屏幕状态: {final_debug}")

        return False

    def _try_close_by_ui_hierarchy(self) -> bool:
        """通过UI层次结构查找关闭按钮 - 增强版本"""
        try:
            # 1. 通过resource-id查找
            for res_id in self.close_ids:
                element = self.device(resourceId=res_id)
                if element.exists(timeout=1.0):
                    log.debug(f"找到关闭按钮 (resource-id): {res_id}")
                    element.click()
                    time.sleep(0.5)
                    return True

            # 2. 通过文本查找
            for text in self.close_texts:
                # 精确匹配
                element = self.device(text=text)
                if element.exists(timeout=1.0):
                    log.debug(f"找到关闭按钮 (文本): {text}")
                    element.click()
                    time.sleep(0.5)
                    return True

                # 包含匹配
                element = self.device(textContains=text)
                if element.exists(timeout=1.0):
                    log.debug(f"找到关闭按钮 (包含文本): {text}")
                    element.click()
                    time.sleep(0.5)
                    return True

            # 3. 通过className查找常见的关闭按钮
            button_classes = [
                "android.widget.ImageButton",
                "android.widget.Button",
                "android.widget.ImageView",
                "android.view.View"
            ]

            for class_name in button_classes:
                close_buttons = self.device(className=class_name)
                if close_buttons.exists(timeout=1.0):
                    for i in range(min(close_buttons.count, 5)):  # 限制检查数量
                        button = close_buttons[i] if close_buttons.count > 1 else close_buttons
                        if self._is_close_button_by_position(button) or self._is_close_button_by_content(button):
                            log.debug(f"找到位置/内容合适的{class_name}关闭按钮")
                            button.click()
                            time.sleep(0.5)
                            return True

            # 4. 查找包含关闭图标的元素
            if self._try_close_by_icon():
                return True

        except Exception as e:
            log.debug(f"UI层次结构检测失败: {e}")

        return False

    def _try_close_by_position(self) -> bool:
        """通过位置检测关闭按钮 - 增强版本"""
        try:
            screen_width = self.device.info['displayWidth']
            screen_height = self.device.info['displayHeight']

            # 定义多个可能的关闭按钮位置区域
            position_areas = [
                # 右上角
                {
                    'name': '右上角',
                    'bounds': (int(screen_width * 0.7), 0, screen_width, int(screen_height * 0.3))
                },
                # 左上角
                {
                    'name': '左上角',
                    'bounds': (0, 0, int(screen_width * 0.3), int(screen_height * 0.3))
                },
                # 右下角
                {
                    'name': '右下角',
                    'bounds': (int(screen_width * 0.7), int(screen_height * 0.7), screen_width, screen_height)
                },
                # 屏幕顶部中央
                {
                    'name': '顶部中央',
                    'bounds': (int(screen_width * 0.3), 0, int(screen_width * 0.7), int(screen_height * 0.2))
                }
            ]

            for area in position_areas:
                try:
                    elements = self.device(clickable=True, bounds=area['bounds'])

                    if elements.exists(timeout=1.0):
                        # 如果有多个元素，选择最小的（更可能是关闭按钮）
                        if elements.count > 1:
                            smallest_element = None
                            smallest_size = float('inf')

                            for i in range(min(elements.count, 3)):  # 只检查前3个
                                element = elements[i]
                                try:
                                    bounds = element.info.get('bounds', {})
                                    if bounds:
                                        width = bounds.get('right', 0) - bounds.get('left', 0)
                                        height = bounds.get('bottom', 0) - bounds.get('top', 0)
                                        size = width * height

                                        if size < smallest_size and size > 0:
                                            smallest_size = size
                                            smallest_element = element
                                except:
                                    continue

                            if smallest_element:
                                log.debug(f"在{area['name']}找到最小可点击元素，尝试点击")
                                smallest_element.click()
                                time.sleep(0.5)
                                return True
                        else:
                            log.debug(f"在{area['name']}找到可点击元素，尝试点击")
                            elements.click()
                            time.sleep(0.5)
                            return True

                except Exception as e:
                    log.debug(f"{area['name']}区域检测失败: {e}")
                    continue

        except Exception as e:
            log.debug(f"位置检测失败: {e}")

        return False

    def _try_back_key(self) -> bool:
        """尝试使用返回键关闭弹窗"""
        try:
            log.debug("尝试使用返回键关闭弹窗")
            self.device.press("back")
            time.sleep(0.5)
            return True
        except Exception as e:
            log.debug(f"返回键操作失败: {e}")
            return False

    def _is_close_button_by_position(self, element) -> bool:
        """根据位置判断是否为关闭按钮 - 增强版本"""
        try:
            bounds = element.info.get('bounds', {})
            if not bounds:
                return False

            screen_width = self.device.info['displayWidth']
            screen_height = self.device.info['displayHeight']

            element_left = bounds.get('left', 0)
            element_right = bounds.get('right', 0)
            element_top = bounds.get('top', 0)
            element_bottom = bounds.get('bottom', 0)

            # 检查是否在右上角区域
            if (element_right > screen_width * 0.7 and
                element_top < screen_height * 0.3):
                return True

            # 检查是否在左上角区域（某些应用的关闭按钮在左上角）
            if (element_left < screen_width * 0.3 and
                element_top < screen_height * 0.3):
                return True

            # 检查是否在弹窗的边角位置
            element_width = element_right - element_left
            element_height = element_bottom - element_top

            # 小尺寸元素更可能是关闭按钮
            if (element_width < 100 and element_height < 100 and
                (element_right > screen_width * 0.6 or element_left < screen_width * 0.4)):
                return True

        except Exception:
            return False

        return False

    def _is_close_button_by_content(self, element) -> bool:
        """根据内容判断是否为关闭按钮"""
        try:
            element_info = element.info
            text = element_info.get('text', '').lower()
            content_desc = element_info.get('contentDescription', '').lower()
            resource_id = element_info.get('resourceId', '').lower()

            # 检查文本内容
            close_indicators = ['x', '×', '✕', 'close', 'dismiss', 'cancel', '关闭', '取消']
            combined_content = f"{text} {content_desc} {resource_id}"

            for indicator in close_indicators:
                if indicator in combined_content:
                    return True

            # 检查是否为单字符且可能是关闭符号
            if len(text.strip()) == 1 and text.strip() in ['x', '×', '✕']:
                return True

        except Exception:
            return False

        return False

    def _try_close_by_icon(self) -> bool:
        """通过图标特征查找关闭按钮"""
        try:
            # 查找可能包含关闭图标的元素
            icon_selectors = [
                {'contentDescription': 'Close'},
                {'contentDescription': 'Dismiss'},
                {'contentDescription': 'Cancel'},
                {'contentDescription': '关闭'},
                {'contentDescription': '取消'},
                {'text': 'X'},
                {'text': '×'},
                {'text': '✕'},
            ]

            for selector in icon_selectors:
                elements = self.device(**selector)
                if elements.exists(timeout=1.0):
                    log.debug(f"找到图标关闭按钮: {selector}")
                    elements.click()
                    time.sleep(0.5)
                    return True

        except Exception as e:
            log.debug(f"图标检测失败: {e}")

        return False

    def is_popup_present(self, fast_mode: bool = False) -> bool:
        """检测是否有弹窗出现 - 增强版本

        Args:
            fast_mode: 快速模式，减少检测时间
        """
        try:
            log.debug(f"开始弹窗检测... (快速模式: {fast_mode})")

            # 设置超时时间
            timeout = 0.3 if fast_mode else 0.5

            # 方法0: 优先检查已知的关闭按钮 - 如果有关闭按钮，很可能有弹窗
            for res_id in self.close_ids[:5]:  # 快速模式只检查前5个
                if self.device(resourceId=res_id).exists(timeout=timeout):
                    log.debug(f"检测到关闭按钮，推断存在弹窗: {res_id}")
                    return True

            # 方法1: 检查常见的弹窗类型
            popup_classes_to_check = self.popup_classes[:6] if fast_mode else self.popup_classes
            for class_name in popup_classes_to_check:
                elements = self.device(className=class_name)
                if elements.exists(timeout=timeout):
                    # 快速模式下简化验证
                    if fast_mode:
                        log.debug(f"检测到弹窗类型: {class_name}")
                        return True

                    # 进一步验证是否真的是弹窗
                    try:
                        count = elements.count
                        if count > 0:
                            log.debug(f"检测到弹窗类型: {class_name} (数量: {count})")
                            # 对于某些类型，需要进一步验证
                            if class_name in ['android.widget.FrameLayout', 'android.view.ViewGroup']:
                                # 检查是否有子元素或特定属性
                                element_info = elements.info if count == 1 else None
                                if element_info and element_info.get('childCount', 0) > 0:
                                    return True
                            else:
                                return True
                    except:
                        # 如果获取详细信息失败，但元素存在，仍然认为可能是弹窗
                        return True

            # 快速模式下跳过复杂检测
            if fast_mode:
                log.debug("快速模式：跳过复杂检测")
                return False

            # 方法2: 检查是否有包含关键词的文本元素（只检查前几个）
            for keyword in self.close_texts[:5]:
                if self.device(textContains=keyword).exists(timeout=timeout):
                    log.debug(f"检测到弹窗关键词: {keyword}")
                    return True

            # 方法3: 检查弹窗特征关键词（只检查前几个）
            for keyword in self.popup_keywords[:5]:
                if self.device(textContains=keyword).exists(timeout=timeout):
                    log.debug(f"检测到弹窗特征: {keyword}")
                    return True

            # 方法4: 检查角落关闭按钮（最快的检测方法）
            if self._detect_corner_close_buttons(fast_mode=fast_mode):
                log.debug("检测到角落关闭按钮")
                return True

        except Exception as e:
            log.debug(f"弹窗检测失败: {e}")

        log.debug("未检测到弹窗")
        return False

    def _detect_corner_close_buttons(self, fast_mode: bool = False) -> bool:
        """检测角落的关闭按钮"""
        try:
            screen_width = self.device.info['displayWidth']
            screen_height = self.device.info['displayHeight']

            # 快速模式下只检查右上角
            if fast_mode:
                corner_areas = [
                    # 右上角
                    (int(screen_width * 0.8), 0, screen_width, int(screen_height * 0.2)),
                ]
            else:
                corner_areas = [
                    # 右上角
                    (int(screen_width * 0.7), 0, screen_width, int(screen_height * 0.3)),
                    # 左上角
                    (0, 0, int(screen_width * 0.3), int(screen_height * 0.3)),
                ]

            timeout = 0.2 if fast_mode else 0.3

            for area in corner_areas:
                try:
                    elements = self.device(
                        clickable=True,
                        bounds=area
                    )

                    if elements.exists(timeout=timeout):
                        # 快速模式下简化检查
                        if fast_mode:
                            return True

                        # 检查是否有小尺寸元素（可能是关闭按钮）
                        if elements.count == 1:
                            element_info = elements.info
                            bounds = element_info.get('bounds', {})
                            if bounds:
                                width = bounds.get('right', 0) - bounds.get('left', 0)
                                height = bounds.get('bottom', 0) - bounds.get('top', 0)
                                # 小尺寸且在角落，很可能是关闭按钮
                                if width < 150 and height < 150:
                                    return True
                        elif elements.count > 1:
                            # 多个元素时，检查是否有小尺寸的
                            for i in range(min(elements.count, 2)):  # 减少检查数量
                                element = elements[i]
                                try:
                                    bounds = element.info.get('bounds', {})
                                    if bounds:
                                        width = bounds.get('right', 0) - bounds.get('left', 0)
                                        height = bounds.get('bottom', 0) - bounds.get('top', 0)
                                        if width < 150 and height < 150:
                                            return True
                                except:
                                    continue
                except:
                    continue

        except Exception as e:
            log.debug(f"角落按钮检测失败: {e}")

        return False

    def _detect_overlay_popup(self) -> bool:
        """检测覆盖层弹窗"""
        try:
            # 检查是否有全屏或大面积的覆盖层
            screen_width = self.device.info['displayWidth']
            screen_height = self.device.info['displayHeight']

            # 查找可能的覆盖层元素
            overlay_elements = self.device(
                clickable=True,
                bounds=(0, 0, screen_width, screen_height)
            )

            if overlay_elements.exists(timeout=1.0):
                # 检查是否有子元素，覆盖层通常包含其他UI元素
                try:
                    element_info = overlay_elements.info
                    if element_info.get('childCount', 0) > 0:
                        return True
                except:
                    pass

            # 检查半透明覆盖层
            for alpha_class in ['android.widget.FrameLayout', 'android.view.ViewGroup']:
                elements = self.device(className=alpha_class)
                if elements.exists(timeout=1.0):
                    for i in range(min(elements.count, 3)):  # 只检查前3个
                        element = elements[i] if elements.count > 1 else elements
                        try:
                            bounds = element.info.get('bounds', {})
                            if bounds:
                                width = bounds.get('right', 0) - bounds.get('left', 0)
                                height = bounds.get('bottom', 0) - bounds.get('top', 0)
                                # 如果元素占据大部分屏幕空间，可能是覆盖层
                                if width > screen_width * 0.8 and height > screen_height * 0.6:
                                    return True
                        except:
                            continue

        except Exception as e:
            log.debug(f"覆盖层检测失败: {e}")

        return False

    def _detect_center_floating_elements(self) -> bool:
        """检测屏幕中央的悬浮元素"""
        try:
            screen_width = self.device.info['displayWidth']
            screen_height = self.device.info['displayHeight']

            # 检查屏幕中央区域
            center_x = screen_width // 2
            center_y = screen_height // 2

            # 定义中央区域范围
            center_bounds = (
                int(screen_width * 0.2), int(screen_height * 0.2),
                int(screen_width * 0.8), int(screen_height * 0.8)
            )

            # 查找中央区域的可点击元素
            center_elements = self.device(
                clickable=True,
                bounds=center_bounds
            )

            if center_elements.exists(timeout=1.0):
                # 进一步验证是否为弹窗
                try:
                    element_info = center_elements.info
                    # 检查元素是否有弹窗特征
                    class_name = element_info.get('className', '')
                    if any(popup_class in class_name for popup_class in self.popup_classes):
                        return True

                    # 检查是否包含弹窗相关文本
                    text = element_info.get('text', '')
                    content_desc = element_info.get('contentDescription', '')
                    combined_text = f"{text} {content_desc}".lower()

                    for keyword in self.popup_keywords:
                        if keyword.lower() in combined_text:
                            return True

                except:
                    pass

        except Exception as e:
            log.debug(f"中央悬浮元素检测失败: {e}")

        return False

    def _detect_mask_layer(self) -> bool:
        """检测遮罩层"""
        try:
            # 查找可能的遮罩层元素
            mask_selectors = [
                {'className': 'android.view.View', 'clickable': True},
                {'className': 'android.widget.FrameLayout', 'clickable': True},
                {'resourceId': 'android:id/content'}
            ]

            for selector in mask_selectors:
                elements = self.device(**selector)
                if elements.exists(timeout=1.0):
                    try:
                        # 检查元素的层级和大小
                        element_info = elements.info
                        bounds = element_info.get('bounds', {})
                        if bounds:
                            width = bounds.get('right', 0) - bounds.get('left', 0)
                            height = bounds.get('bottom', 0) - bounds.get('top', 0)
                            screen_width = self.device.info['displayWidth']
                            screen_height = self.device.info['displayHeight']

                            # 如果元素几乎覆盖整个屏幕，可能是遮罩层
                            if (width > screen_width * 0.9 and height > screen_height * 0.9 and
                                element_info.get('childCount', 0) > 0):
                                return True
                    except:
                        continue

        except Exception as e:
            log.debug(f"遮罩层检测失败: {e}")

        return False

    def is_popup_present_ultra_fast(self) -> bool:
        """超快速弹窗检测（只检查最关键的指标）"""
        try:
            # 只检查已知的关闭按钮（最可靠的指标）
            for res_id in self.close_ids[:3]:  # 只检查前3个最常见的
                if self.device(resourceId=res_id).exists(timeout=0.1):
                    log.debug(f"超快速检测到关闭按钮: {res_id}")
                    return True

            # 检查最常见的弹窗类型
            common_popup_classes = [
                'android.app.Dialog',
                'android.app.AlertDialog',
            ]

            for class_name in common_popup_classes:
                if self.device(className=class_name).exists(timeout=0.1):
                    log.debug(f"超快速检测到弹窗类型: {class_name}")
                    return True

            return False

        except Exception as e:
            log.debug(f"超快速弹窗检测失败: {e}")
            return False

    def detect_and_close_popup_once(self, debug_mode: bool = False) -> bool:
        """
        检测并关闭弹窗（只检测一次，不等待）

        Args:
            debug_mode: 是否启用调试模式

        Returns:
            bool: 是否成功处理弹窗
        """
        log.info("执行单次弹窗检测和关闭")

        # 检测是否有弹窗（使用超快速模式）
        if not self.is_popup_present_ultra_fast():
            log.info("未检测到弹窗，无需处理")
            return False

        log.info("检测到弹窗，尝试关闭...")

        # 尝试各种关闭方法
        try:
            # 1. 尝试通过UI层次结构关闭
            if self._try_close_by_ui_hierarchy():
                log.info("通过UI层次结构成功关闭弹窗")
                # 使用更可靠的验证方法
                if self._verify_popup_closed():
                    return True
                else:
                    log.warning("弹窗似乎未完全关闭，继续尝试其他方法")

            # 2. 尝试通过位置检测关闭
            if self._try_close_by_position():
                log.info("通过位置检测成功关闭弹窗")
                if self._verify_popup_closed():
                    return True
                else:
                    log.warning("位置关闭后弹窗仍存在，继续尝试")

            # 3. 尝试返回键
            if self._try_back_key():
                log.info("通过返回键成功关闭弹窗")
                if self._verify_popup_closed():
                    return True
                else:
                    log.warning("返回键后弹窗仍存在")

            log.warning("所有关闭方法都未成功")
            return False

        except Exception as e:
            log.error(f"单次弹窗关闭过程中出现异常: {e}")
            if debug_mode:
                import traceback
                log.error(f"异常详情: {traceback.format_exc()}")
            return False

    def _verify_popup_closed(self, max_wait: float = 1.5) -> bool:
        """
        智能验证弹窗是否已关闭

        Args:
            max_wait: 最大等待时间（秒）

        Returns:
            bool: 弹窗是否已关闭
        """
        try:
            # 等待UI更新
            time.sleep(0.3)

            # 记录关闭前的状态作为基准
            initial_check = self.is_popup_present_ultra_fast()

            # 如果超快速检测显示无弹窗，很可能已经关闭
            if not initial_check:
                log.debug("超快速验证：弹窗已关闭")
                return True

            # 如果仍有弹窗，等待更长时间（可能是动画或UI更新延迟）
            time.sleep(0.5)

            # 再次检测，使用更宽松的标准
            second_check = self.is_popup_present_ultra_fast()

            if not second_check:
                log.debug("延迟验证：弹窗已关闭")
                return True

            # 如果仍然检测到弹窗，检查是否是同一个弹窗
            # 通过检查关闭按钮是否还存在来判断
            close_button_exists = False
            for res_id in self.close_ids[:3]:  # 只检查前3个最常见的
                if self.device(resourceId=res_id).exists(timeout=0.2):
                    close_button_exists = True
                    break

            if not close_button_exists:
                log.debug("关闭按钮已消失，认为弹窗已关闭")
                return True

            log.debug("验证失败：弹窗和关闭按钮仍然存在")
            return False

        except Exception as e:
            log.debug(f"验证弹窗关闭状态时出错: {e}")
            # 出错时采用乐观策略：认为关闭操作成功
            log.debug("验证出错，采用乐观策略认为弹窗已关闭")
            return True

    def debug_current_screen(self) -> Dict[str, Any]:
        """调试当前屏幕状态，帮助诊断弹窗检测问题"""
        debug_info = {
            'screen_info': {},
            'detected_elements': [],
            'popup_indicators': [],
            'clickable_elements': [],
            'potential_close_buttons': []
        }

        try:
            # 获取屏幕基本信息
            debug_info['screen_info'] = {
                'width': self.device.info.get('displayWidth', 0),
                'height': self.device.info.get('displayHeight', 0),
                'orientation': self.device.info.get('displayRotation', 0)
            }

            # 检测弹窗类型元素
            for class_name in self.popup_classes:
                try:
                    elements = self.device(className=class_name)
                    if elements.exists(timeout=0.5):
                        debug_info['detected_elements'].append({
                            'type': 'popup_class',
                            'className': class_name,
                            'count': elements.count,
                            'info': elements.info if elements.count == 1 else 'multiple'
                        })
                except:
                    continue

            # 检测弹窗关键词
            for keyword in self.popup_keywords[:10]:  # 只检查前10个关键词
                try:
                    elements = self.device(textContains=keyword)
                    if elements.exists(timeout=0.5):
                        debug_info['popup_indicators'].append({
                            'type': 'keyword',
                            'keyword': keyword,
                            'count': elements.count
                        })
                except:
                    continue

            # 检测可点击元素
            try:
                clickable = self.device(clickable=True)
                if clickable.exists(timeout=0.5):
                    debug_info['clickable_elements'] = {
                        'total_count': clickable.count,
                        'sample_info': []
                    }

                    # 获取前5个可点击元素的信息
                    for i in range(min(clickable.count, 5)):
                        element = clickable[i] if clickable.count > 1 else clickable
                        try:
                            info = element.info
                            debug_info['clickable_elements']['sample_info'].append({
                                'className': info.get('className', ''),
                                'text': info.get('text', ''),
                                'resourceId': info.get('resourceId', ''),
                                'bounds': info.get('bounds', {}),
                                'contentDescription': info.get('contentDescription', '')
                            })
                        except:
                            continue
            except:
                pass

            # 检测潜在的关闭按钮
            for res_id in self.close_ids[:10]:  # 只检查前10个ID
                try:
                    element = self.device(resourceId=res_id)
                    if element.exists(timeout=0.5):
                        debug_info['potential_close_buttons'].append({
                            'type': 'resource_id',
                            'resourceId': res_id,
                            'info': element.info
                        })
                except:
                    continue

            log.info(f"屏幕调试信息: {debug_info}")

        except Exception as e:
            log.error(f"调试信息收集失败: {e}")
            debug_info['error'] = str(e)

        return debug_info

    def safe_action(self, action_func: Callable, *args, **kwargs) -> Any:
        """
        执行操作前后检查并处理弹窗
        
        Args:
            action_func: 要执行的操作函数
            *args: 操作函数的位置参数
            **kwargs: 操作函数的关键字参数
            
        Returns:
            操作函数的返回值
        """
        # 执行操作前检查弹窗
        self.detect_and_close_popup(timeout=3)

        # 执行主要操作
        try:
            result = action_func(*args, **kwargs)
        except Exception as e:
            log.error(f"执行操作失败: {e}")
            raise

        # 执行操作后再次检查弹窗
        self.detect_and_close_popup(timeout=5)

        return result

    def click_with_popup_handling(self, x: int, y: int) -> bool:
        """
        带弹窗处理的点击操作
        
        Args:
            x: 点击的x坐标
            y: 点击的y坐标
            
        Returns:
            bool: 操作是否成功
        """
        try:
            return self.safe_action(self.device.click, x, y)
        except Exception as e:
            log.error(f"点击操作失败: {e}")
            return False

    def input_with_popup_handling(self, text: str) -> bool:
        """
        带弹窗处理的输入操作
        
        Args:
            text: 要输入的文本
            
        Returns:
            bool: 操作是否成功
        """
        try:
            return self.safe_action(self.device.send_keys, text)
        except Exception as e:
            log.error(f"输入操作失败: {e}")
            return False

    def element_click_with_popup_handling(self, selector: Dict[str, Any]) -> bool:
        """
        带弹窗处理的元素点击操作
        
        Args:
            selector: 元素选择器字典，如 {'resourceId': 'com.example:id/button'}
            
        Returns:
            bool: 操作是否成功
        """
        try:
            def click_element():
                element = self.device(**selector)
                if element.exists(timeout=10):
                    element.click()
                    return True
                else:
                    raise TimeoutError(f"元素未找到: {selector}")
            
            return self.safe_action(click_element)
        except Exception as e:
            log.error(f"元素点击操作失败: {e}")
            return False


class AutomationWithPopupTool:
    """集成弹窗处理的自动化操作类"""

    def __init__(self, device: u2.Device = None):
        """
        初始化自动化操作类
        
        Args:
            device: UIAutomator2设备对象
        """
        if device is None:
            self.device = u2.connect()
        else:
            self.device = device
        
        self.popup_tool = PopupTool(self.device)

    def click(self, x: int, y: int) -> bool:
        """带弹窗处理的点击"""
        return self.popup_tool.click_with_popup_handling(x, y)

    def input_text(self, text: str) -> bool:
        """带弹窗处理的输入"""
        return self.popup_tool.input_with_popup_handling(text)

    def click_element(self, selector: Dict[str, Any]) -> bool:
        """带弹窗处理的元素点击"""
        return self.popup_tool.element_click_with_popup_handling(selector)

    def detect_and_close_popup(self, timeout: int = 10, wait_for_popup: bool = False) -> bool:
        """检测并关闭弹窗"""
        return self.popup_tool.detect_and_close_popup(timeout, wait_for_popup=wait_for_popup)

    def detect_and_close_popup_once(self, debug_mode: bool = False) -> bool:
        """检测并关闭弹窗（只检测一次，不等待）"""
        return self.popup_tool.detect_and_close_popup_once(debug_mode)

    def is_popup_present(self) -> bool:
        """检测是否有弹窗"""
        return self.popup_tool.is_popup_present()

    def is_popup_present_ultra_fast(self) -> bool:
        """超快速弹窗检测"""
        return self.popup_tool.is_popup_present_ultra_fast()


# 便捷函数
def create_popup_tool(device: u2.Device = None) -> PopupTool:
    """
    创建弹窗处理工具实例

    Args:
        device: UIAutomator2设备对象，如果为None则使用默认连接

    Returns:
        PopupTool: 弹窗处理工具实例
    """
    return PopupTool(device)


def create_automation_with_popup(device: u2.Device = None) -> AutomationWithPopupTool:
    """
    创建集成弹窗处理的自动化操作实例

    Args:
        device: UIAutomator2设备对象，如果为None则使用默认连接

    Returns:
        AutomationWithPopupTool: 自动化操作实例
    """
    return AutomationWithPopupTool(device)


# 使用示例
if __name__ == "__main__":
    # 示例1: 基本弹窗处理
    from core.base_driver import driver_manager


    popup_tool = create_popup_tool(driver_manager.driver)

    # # 检测并关闭弹窗
    if popup_tool.detect_and_close_popup_once():
        log.info("成功处理弹窗")
    else:
        log.info("未发现弹窗或处理失败")

    # # 示例2: 带弹窗处理的自动化操作
    # automation = create_automation_with_popup()
    #
    # # 执行点击操作，自动处理弹窗
    # automation.click(500, 300)
    #
    # # 执行输入操作，自动处理弹窗
    # automation.input_text("测试文本")
    #
    # # 点击元素，自动处理弹窗
    # automation.click_element({'resourceId': 'com.example:id/button'})
    #
    # # 示例3: 手动控制弹窗处理
    # popup_tool = create_popup_tool()
    #
    # # 检查是否有弹窗
    # if popup_tool.is_popup_present():
    #     log.info("检测到弹窗")
    #     # 尝试关闭弹窗（启用调试模式）
    #     if popup_tool.detect_and_close_popup(timeout=15, debug_mode=True):
    #         log.info("弹窗已关闭")
    #     else:
    #         log.error("弹窗关闭失败")
    #         # 输出调试信息
    #         debug_info = popup_tool.debug_current_screen()
    #         log.error(f"调试信息: {debug_info}")
    # else:
    #     log.info("未检测到弹窗")
    #     # 即使未检测到弹窗，也可以输出调试信息
    #     debug_info = popup_tool.debug_current_screen()
    #     log.info(f"当前屏幕状态: {debug_info}")
    #
    # # 示例4: 在现有操作中集成弹窗处理
    # def my_custom_operation():
    #     """自定义操作示例"""
    #     # 执行一些操作
    #     popup_tool.device.click(100, 200)
    #     time.sleep(1)
    #     popup_tool.device.send_keys("hello")
    #     return "操作完成"
    #
    # # 使用safe_action包装自定义操作
    # result = popup_tool.safe_action(my_custom_operation)
