"""
Ella应用元素探测工具
探测Ella语音助手应用的UI元素结构
"""
import json
import time
from core.logger import log
from core.base_driver import driver_manager


def explore_ella_app():
    """探测Ella应用的元素结构"""
    log.info("🔍 开始探测Ella应用元素结构")
    log.info("=" * 80)
    
    try:
        # 启动Ella应用
        log.info("启动Ella应用...")
        driver_manager.start_app("com.transsion.aivoiceassistant")
        
        # 等待应用加载
        time.sleep(5)
        
        # 截图记录初始状态
        screenshot_path = driver_manager.screenshot("ella_initial_exploration.png", use_test_class_dir=False)
        log.info(f"初始状态截图: {screenshot_path}")
        
        # 获取页面结构
        log.info("获取页面XML结构...")
        xml_content = driver_manager.driver.dump_hierarchy()
        
        # 保存XML文件
        xml_path = "reports/ella_page_structure.xml"
        with open(xml_path, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        log.info(f"页面结构保存: {xml_path}")
        
        # 查找所有可交互元素
        log.info("查找可交互元素...")
        
        # 查找输入框
        input_elements = []
        edit_texts = driver_manager.driver(className="android.widget.EditText")
        for et in edit_texts:
            try:
                info = et.info
                input_elements.append({
                    'type': 'EditText',
                    'resource_id': info.get('resourceId', ''),
                    'text': info.get('text', ''),
                    'content_desc': info.get('contentDescription', ''),
                    'bounds': info.get('bounds', {}),
                    'enabled': info.get('enabled', False),
                    'focusable': info.get('focusable', False)
                })
                log.info(f"  📝 输入框: {info.get('resourceId', '')} - {info.get('text', '')}")
            except:
                pass
        
        # 查找按钮
        button_elements = []
        buttons = driver_manager.driver(className="android.widget.Button")
        for btn in buttons:
            try:
                info = btn.info
                button_elements.append({
                    'type': 'Button',
                    'resource_id': info.get('resourceId', ''),
                    'text': info.get('text', ''),
                    'content_desc': info.get('contentDescription', ''),
                    'bounds': info.get('bounds', {}),
                    'enabled': info.get('enabled', False),
                    'clickable': info.get('clickable', False)
                })
                log.info(f"  🔘 按钮: {info.get('resourceId', '')} - {info.get('text', '')}")
            except:
                pass
        
        # 查找图像视图（可能的图标）
        image_elements = []
        images = driver_manager.driver(className="android.widget.ImageView")
        for img in images:
            try:
                info = img.info
                if info.get('clickable', False) or info.get('contentDescription', ''):
                    image_elements.append({
                        'type': 'ImageView',
                        'resource_id': info.get('resourceId', ''),
                        'content_desc': info.get('contentDescription', ''),
                        'bounds': info.get('bounds', {}),
                        'clickable': info.get('clickable', False)
                    })
                    log.info(f"  🖼️ 图像: {info.get('resourceId', '')} - {info.get('contentDescription', '')}")
            except:
                pass
        
        # 查找文本视图
        text_elements = []
        text_views = driver_manager.driver(className="android.widget.TextView")
        for tv in text_views:
            try:
                info = tv.info
                text = info.get('text', '')
                if text and len(text) > 2:  # 过滤掉太短的文本
                    text_elements.append({
                        'type': 'TextView',
                        'resource_id': info.get('resourceId', ''),
                        'text': text,
                        'bounds': info.get('bounds', {}),
                        'clickable': info.get('clickable', False)
                    })
                    log.info(f"  📄 文本: {info.get('resourceId', '')} - {text[:50]}...")
            except:
                pass
        
        # 查找RecyclerView（可能的聊天列表）
        recycler_elements = []
        recyclers = driver_manager.driver(className="androidx.recyclerview.widget.RecyclerView")
        for rv in recyclers:
            try:
                info = rv.info
                recycler_elements.append({
                    'type': 'RecyclerView',
                    'resource_id': info.get('resourceId', ''),
                    'bounds': info.get('bounds', {}),
                    'scrollable': info.get('scrollable', False)
                })
                log.info(f"  📋 列表: {info.get('resourceId', '')}")
            except:
                pass
        
        # 汇总所有元素
        all_elements = {
            'input_elements': input_elements,
            'button_elements': button_elements,
            'image_elements': image_elements,
            'text_elements': text_elements[:10],  # 只保留前10个文本元素
            'recycler_elements': recycler_elements,
            'exploration_time': time.strftime("%Y-%m-%d %H:%M:%S"),
            'total_elements': len(input_elements) + len(button_elements) + len(image_elements) + len(recycler_elements)
        }
        
        # 保存元素信息
        elements_path = "reports/ella_elements.json"
        with open(elements_path, 'w', encoding='utf-8') as f:
            json.dump(all_elements, f, ensure_ascii=False, indent=2)
        log.info(f"元素信息保存: {elements_path}")
        
        # 尝试查找语音输入相关元素
        log.info("\n🎤 查找语音输入相关元素...")
        voice_keywords = ['voice', 'mic', 'microphone', '语音', '麦克风', 'speak']
        
        for keyword in voice_keywords:
            # 通过描述查找
            elements = driver_manager.driver(descriptionContains=keyword)
            for elem in elements:
                try:
                    info = elem.info
                    log.info(f"  🎤 语音元素(描述): {info.get('resourceId', '')} - {info.get('contentDescription', '')}")
                except:
                    pass
            
            # 通过文本查找
            elements = driver_manager.driver(textContains=keyword)
            for elem in elements:
                try:
                    info = elem.info
                    log.info(f"  🎤 语音元素(文本): {info.get('resourceId', '')} - {info.get('text', '')}")
                except:
                    pass
        
        # 尝试查找发送相关元素
        log.info("\n📤 查找发送相关元素...")
        send_keywords = ['send', 'submit', '发送', '提交', 'go']
        
        for keyword in send_keywords:
            # 通过文本查找
            elements = driver_manager.driver(textContains=keyword)
            for elem in elements:
                try:
                    info = elem.info
                    log.info(f"  📤 发送元素: {info.get('resourceId', '')} - {info.get('text', '')}")
                except:
                    pass
        
        # 统计信息
        log.info("\n📊 元素统计:")
        log.info(f"  输入框: {len(input_elements)} 个")
        log.info(f"  按钮: {len(button_elements)} 个")
        log.info(f"  图像: {len(image_elements)} 个")
        log.info(f"  文本: {len(text_elements)} 个")
        log.info(f"  列表: {len(recycler_elements)} 个")
        log.info(f"  总计: {all_elements['total_elements']} 个可交互元素")
        
        # 最终截图
        final_screenshot = driver_manager.screenshot("ella_exploration_completed.png", use_test_class_dir=False)
        log.info(f"最终截图: {final_screenshot}")
        
        # 停止应用
        driver_manager.stop_app("com.transsion.aivoiceassistant")
        
        log.info("\n✅ Ella应用元素探测完成")
        log.info("📁 生成的文件:")
        log.info(f"  - {xml_path} (页面XML结构)")
        log.info(f"  - {elements_path} (元素信息JSON)")
        log.info(f"  - {screenshot_path} (初始截图)")
        log.info(f"  - {final_screenshot} (最终截图)")
        
        return all_elements
        
    except Exception as e:
        log.error(f"❌ Ella应用元素探测失败: {e}")
        return None


def test_ella_basic_interaction():
    """测试Ella基本交互"""
    log.info("🧪 测试Ella基本交互")
    log.info("=" * 80)
    
    try:
        # 启动应用
        driver_manager.start_app("com.transsion.aivoiceassistant")
        time.sleep(3)
        
        # 尝试找到输入框并输入测试文本
        log.info("尝试文本输入...")
        
        # 方法1: 通过className查找
        edit_texts = driver_manager.driver(className="android.widget.EditText")
        if edit_texts:
            try:
                edit_text = edit_texts[0]
                edit_text.click()
                time.sleep(1)
                edit_text.send_keys("hello")
                log.info("✅ 方法1成功: 通过className找到输入框")
                
                # 截图
                driver_manager.screenshot("ella_text_input_test.png", use_test_class_dir=False)
                
                # 清空
                edit_text.clear()
                
            except Exception as e:
                log.warning(f"方法1失败: {e}")
        
        # 方法2: 尝试点击屏幕中央（可能的输入区域）
        try:
            width, height = driver_manager.get_window_size()
            center_x, center_y = width // 2, height // 2
            
            log.info(f"尝试点击屏幕中央: ({center_x}, {center_y})")
            driver_manager.driver.click(center_x, center_y)
            time.sleep(1)
            
            # 尝试输入
            driver_manager.driver.send_keys("test input")
            log.info("✅ 方法2成功: 点击屏幕中央输入")
            
            # 截图
            driver_manager.screenshot("ella_center_click_test.png", use_test_class_dir=False)
            
        except Exception as e:
            log.warning(f"方法2失败: {e}")
        
        # 方法3: 查找特定的resourceId
        possible_ids = [
            "com.transsion.aivoiceassistant:id/input_text",
            "com.transsion.aivoiceassistant:id/edit_text",
            "com.transsion.aivoiceassistant:id/message_input",
            "com.transsion.aivoiceassistant:id/chat_input"
        ]
        
        for resource_id in possible_ids:
            try:
                element = driver_manager.driver(resourceId=resource_id)
                if element.exists:
                    element.click()
                    time.sleep(1)
                    element.send_keys("test")
                    log.info(f"[SUCCESS] 方法3成功: 找到输入框 {resource_id}")

                    # 截图
                    driver_manager.screenshot(f"ella_input_{resource_id.split(':')[-1]}.png", use_test_class_dir=False)
                    break
            except Exception as e:
                log.debug(f"尝试 {resource_id} 失败: {e}")

        # 停止应用
        driver_manager.stop_app("com.transsion.aivoiceassistant")

        log.info("[SUCCESS] Ella基本交互测试完成")

    except Exception as e:
        log.error(f"[ERROR] Ella基本交互测试失败: {e}")


def main():
    """主函数"""
    log.info("[INFO] Ella应用探测工具")
    log.info("=" * 100)
    
    # 探测元素结构
    elements = explore_ella_app()
    
    if elements:
        log.info("\n🎯 探测成功，建议的页面对象元素定位:")
        log.info("=" * 60)
        
        # 输入框建议
        if elements['input_elements']:
            log.info("📝 输入框定位建议:")
            for elem in elements['input_elements']:
                if elem['resource_id']:
                    log.info(f"  self.input_box = self.create_element(")
                    log.info(f"      {{\"resourceId\": \"{elem['resource_id']}\"}},")
                    log.info(f"      \"输入框\"")
                    log.info(f"  )")
        
        # 按钮建议
        if elements['button_elements']:
            log.info("\n🔘 按钮定位建议:")
            for elem in elements['button_elements']:
                if elem['resource_id'] or elem['text']:
                    locator = {}
                    if elem['resource_id']:
                        locator['resourceId'] = elem['resource_id']
                    if elem['text']:
                        locator['text'] = elem['text']
                    
                    log.info(f"  self.button = self.create_element(")
                    log.info(f"      {locator},")
                    log.info(f"      \"{elem['text'] or '按钮'}\"")
                    log.info(f"  )")
    
    # 测试基本交互
    time.sleep(2)
    test_ella_basic_interaction()
    
    log.info("\n🎉 Ella应用探测完成！")
    log.info("💡 下一步:")
    log.info("1. 查看生成的截图了解应用界面")
    log.info("2. 根据元素信息更新页面对象")
    log.info("3. 运行 'python -m pytest testcases/test_ella/ -v' 测试Ella功能")


if __name__ == "__main__":
    main()
