#!/usr/bin/env python3
"""
指令提取器
从测试脚本中提取command和expected_text，与原Excel数据结合生成新的表格
"""
import os
import sys
import pandas as pd
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from core.logger import log


class CommandExtractor:
    """指令提取器"""
    
    def __init__(self):
        self.project_root = project_root
        self.excel_path = self.project_root / "data" / "query" / "query_new.xlsx"
        self.testcases_dir = self.project_root / "testcases"
        self.output_dir = Path(__file__).parent  # tools/script_generator目录
        
        # 提取失败记录
        self.failed_extractions = []
    
    def load_excel_data(self) -> pd.DataFrame:
        """加载Excel数据"""
        try:
            df = pd.read_excel(self.excel_path)
            log.info(f"成功加载Excel文件，共{len(df)}条记录")
            log.info(f"列名: {df.columns.tolist()}")
            return df
        except Exception as e:
            log.error(f"加载Excel文件失败: {e}")
            raise
    
    def parse_suite_path(self, parent_suite: str, suite: str) -> Optional[Path]:
        """
        解析suite路径，找到对应的测试文件
        
        Args:
            parent_suite: 标签_parentSuite
            suite: 标签_suite
            
        Returns:
            对应的测试文件路径，如果找不到则返回None
        """
        try:
            # 移除 "testcases." 前缀
            if parent_suite.startswith("testcases."):
                parent_suite = parent_suite[10:]
            
            # 将点号替换为路径分隔符
            relative_path = parent_suite.replace(".", os.sep)
            
            # 构建完整路径
            test_file_path = self.testcases_dir / relative_path / f"{suite}.py"
            
            log.debug(f"解析路径: {parent_suite} + {suite} -> {test_file_path}")
            
            if test_file_path.exists():
                return test_file_path
            else:
                log.warning(f"测试文件不存在: {test_file_path}")
                return None
                
        except Exception as e:
            log.error(f"解析suite路径失败: {parent_suite}, {suite}, 错误: {e}")
            return None
    
    def extract_command_from_script(self, script_path: Path) -> Optional[str]:
        """
        从测试脚本中提取command指令
        
        Args:
            script_path: 测试脚本路径
            
        Returns:
            提取的command字符串，如果提取失败则返回None
        """
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试匹配类属性中的 command = "..."
            class_pattern = r'class\s+\w+.*?:\s*"""[^"]*"""[^@]*?command\s*=\s*["\']([^"\']*)["\']'
            match = re.search(class_pattern, content, re.DOTALL)
            if match:
                return match.group(1)
            
            # 尝试匹配方法内的 command = "..."
            method_pattern = r'command\s*=\s*["\']([^"\']*)["\']'
            match = re.search(method_pattern, content)
            if match:
                return match.group(1)
            
            # 如果都没找到，返回None
            log.warning(f"未能从脚本中提取command: {script_path}")
            return None
            
        except Exception as e:
            log.error(f"读取脚本文件失败: {script_path}, 错误: {e}")
            return None
    
    def extract_expected_text_from_script(self, script_path: Path) -> Optional[str]:
        """
        从测试脚本中提取expected_text
        
        Args:
            script_path: 测试脚本路径
            
        Returns:
            提取的expected_text字符串，如果提取失败则返回None
        """
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试匹配类属性中的 expected_text = [...]
            class_pattern = r'class\s+\w+.*?:\s*"""[^"]*"""[^@]*?expected_text\s*=\s*(\[.*?\])'
            match = re.search(class_pattern, content, re.DOTALL)
            if match:
                return match.group(1)
            
            # 尝试匹配方法内的 expected_text = [...]
            method_pattern = r'expected_text\s*=\s*(\[.*?\])'
            match = re.search(method_pattern, content, re.DOTALL)
            if match:
                return match.group(1)
            
            # 如果都没找到，返回None
            log.warning(f"未能从脚本中提取expected_text: {script_path}")
            return None
            
        except Exception as e:
            log.error(f"读取脚本文件失败: {script_path}, 错误: {e}")
            return None
    
    def extract_script_info(self, parent_suite: str, suite: str) -> Dict[str, Any]:
        """
        提取脚本信息
        
        Args:
            parent_suite: 标签_parentSuite
            suite: 标签_suite
            
        Returns:
            包含command和expected_text的字典
        """
        result = {
            'command': None,
            'expected_text': None,
            'script_path': None,
            'extraction_status': 'failed',
            'error_message': None
        }
        
        try:
            # 查找测试文件
            script_path = self.parse_suite_path(parent_suite, suite)
            if not script_path:
                result['error_message'] = '未找到对应的测试文件'
                return result
            
            result['script_path'] = str(script_path)
            
            # 提取command
            command = self.extract_command_from_script(script_path)
            result['command'] = command
            
            # 提取expected_text
            expected_text = self.extract_expected_text_from_script(script_path)
            result['expected_text'] = expected_text
            
            # 判断提取状态
            if command is not None or expected_text is not None:
                result['extraction_status'] = 'partial' if (command is None or expected_text is None) else 'success'
            else:
                result['error_message'] = '未能提取到command和expected_text'
            
            return result
            
        except Exception as e:
            result['error_message'] = f'提取过程中发生错误: {str(e)}'
            log.error(f"提取脚本信息失败: {parent_suite} -> {suite}, 错误: {e}")
            return result
    
    def process_all_records(self) -> pd.DataFrame:
        """
        处理所有记录，提取command和expected_text
        
        Returns:
            包含原始数据和提取信息的DataFrame
        """
        # 加载原始数据
        df = self.load_excel_data()
        
        # 新增列
        df['提取的command'] = None
        df['提取的expected_text'] = None
        df['脚本路径'] = None
        df['提取状态'] = None
        df['错误信息'] = None
        
        log.info("开始处理所有记录...")
        
        for index, row in df.iterrows():
            parent_suite = row.get('标签_parentSuite', '').strip()
            suite = row.get('标签_suite', '').strip()
            
            if not parent_suite or not suite:
                df.at[index, '提取状态'] = 'skipped'
                df.at[index, '错误信息'] = 'parentSuite或suite为空'
                log.warning(f"跳过第{index+1}行，parentSuite或suite为空")
                continue
            
            # 提取脚本信息
            script_info = self.extract_script_info(parent_suite, suite)
            
            # 更新DataFrame
            df.at[index, '提取的command'] = script_info['command']
            df.at[index, '提取的expected_text'] = script_info['expected_text']
            df.at[index, '脚本路径'] = script_info['script_path']
            df.at[index, '提取状态'] = script_info['extraction_status']
            df.at[index, '错误信息'] = script_info['error_message']
            
            # 记录失败的提取
            if script_info['extraction_status'] == 'failed':
                self.failed_extractions.append({
                    'index': index + 1,
                    'parent_suite': parent_suite,
                    'suite': suite,
                    'error': script_info['error_message']
                })
            
            # 打印进度
            status_icon = {
                'success': '✅',
                'partial': '⚠️',
                'failed': '❌',
                'skipped': '⏭️'
            }.get(script_info['extraction_status'], '❓')
            
            print(f"{status_icon} [{index+1}/{len(df)}] {suite}")
        
        log.info(f"处理完成，成功: {len(df[df['提取状态'] == 'success'])}, "
                f"部分: {len(df[df['提取状态'] == 'partial'])}, "
                f"失败: {len(df[df['提取状态'] == 'failed'])}, "
                f"跳过: {len(df[df['提取状态'] == 'skipped'])}")
        
        return df
    
    def save_enhanced_excel(self, df: pd.DataFrame) -> str:
        """
        保存增强的Excel文件
        
        Args:
            df: 包含提取信息的DataFrame
            
        Returns:
            保存的文件路径
        """
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_query_data_{timestamp}.xlsx"
            output_path = self.output_dir / filename
            
            # 保存Excel文件
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主数据表
                df.to_excel(writer, sheet_name='增强数据', index=False)
                
                # 统计信息表
                stats_data = {
                    '状态': ['成功', '部分成功', '失败', '跳过', '总计'],
                    '数量': [
                        len(df[df['提取状态'] == 'success']),
                        len(df[df['提取状态'] == 'partial']),
                        len(df[df['提取状态'] == 'failed']),
                        len(df[df['提取状态'] == 'skipped']),
                        len(df)
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
                
                # 失败记录表
                if self.failed_extractions:
                    failed_df = pd.DataFrame(self.failed_extractions)
                    failed_df.to_excel(writer, sheet_name='失败记录', index=False)
            
            log.info(f"增强Excel文件已保存到: {output_path}")
            return str(output_path)
            
        except Exception as e:
            log.error(f"保存Excel文件失败: {e}")
            raise
    
    def generate_summary_report(self, df: pd.DataFrame, output_path: str) -> str:
        """
        生成汇总报告
        
        Args:
            df: 处理后的DataFrame
            output_path: Excel文件路径
            
        Returns:
            报告文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"command_extraction_report_{timestamp}.md"
            report_path = self.output_dir / report_filename
            
            # 统计信息
            total = len(df)
            success = len(df[df['提取状态'] == 'success'])
            partial = len(df[df['提取状态'] == 'partial'])
            failed = len(df[df['提取状态'] == 'failed'])
            skipped = len(df[df['提取状态'] == 'skipped'])
            
            success_rate = (success / total * 100) if total > 0 else 0
            
            report_content = f"""# 指令提取报告

## 生成时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 统计信息
- **总记录数**: {total}
- **成功提取**: {success} ({success_rate:.1f}%)
- **部分提取**: {partial}
- **提取失败**: {failed}
- **跳过记录**: {skipped}

## 输出文件
- **增强Excel文件**: `{Path(output_path).name}`
- **报告文件**: `{report_filename}`

## 提取详情

### 成功提取的记录
"""
            
            # 成功记录
            success_records = df[df['提取状态'] == 'success']
            for index, row in success_records.iterrows():
                suite = row.get('标签_suite', 'N/A')
                command = row.get('提取的command', 'N/A')
                report_content += f"- **{suite}**: `{command}`\n"
            
            # 失败记录
            if failed > 0:
                report_content += f"\n### 失败记录 ({failed}个)\n\n"
                failed_records = df[df['提取状态'] == 'failed']
                for index, row in failed_records.iterrows():
                    suite = row.get('标签_suite', 'N/A')
                    error = row.get('错误信息', 'N/A')
                    report_content += f"- **{suite}**: {error}\n"
            
            # 部分成功记录
            if partial > 0:
                report_content += f"\n### 部分成功记录 ({partial}个)\n\n"
                partial_records = df[df['提取状态'] == 'partial']
                for index, row in partial_records.iterrows():
                    suite = row.get('标签_suite', 'N/A')
                    command = row.get('提取的command', 'N/A')
                    expected = row.get('提取的expected_text', 'N/A')
                    report_content += f"- **{suite}**: command=`{command}`, expected_text=`{expected}`\n"
            
            report_content += f"""

## 使用说明

1. **增强Excel文件**包含以下新增列：
   - `提取的command`: 从脚本中提取的指令
   - `提取的expected_text`: 从脚本中提取的期望文本
   - `脚本路径`: 对应的测试脚本文件路径
   - `提取状态`: 提取结果状态
   - `错误信息`: 提取失败时的错误信息

2. **提取状态说明**：
   - `success`: 成功提取command和expected_text
   - `partial`: 只提取到command或expected_text中的一个
   - `failed`: 未能提取到任何信息
   - `skipped`: 跳过处理（通常是数据不完整）

3. **后续处理建议**：
   - 对于失败的记录，可以手动检查脚本文件
   - 对于部分成功的记录，可以补充缺失的信息
   - 成功的记录可以直接用于后续的脚本生成
"""
            
            # 写入报告文件
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            log.info(f"汇总报告已生成: {report_path}")
            return str(report_path)
            
        except Exception as e:
            log.error(f"生成汇总报告失败: {e}")
            return ""


def main():
    """主函数"""
    print("🚀 开始提取指令和期望文本...")
    
    extractor = CommandExtractor()
    
    try:
        # 处理所有记录
        df = extractor.process_all_records()
        
        # 保存增强的Excel文件
        output_path = extractor.save_enhanced_excel(df)
        
        # 生成汇总报告
        report_path = extractor.generate_summary_report(df, output_path)
        
        print(f"\n🎉 处理完成！")
        print(f"📊 总记录数: {len(df)}")
        print(f"✅ 成功提取: {len(df[df['提取状态'] == 'success'])}")
        print(f"⚠️  部分提取: {len(df[df['提取状态'] == 'partial'])}")
        print(f"❌ 提取失败: {len(df[df['提取状态'] == 'failed'])}")
        print(f"⏭️  跳过记录: {len(df[df['提取状态'] == 'skipped'])}")
        
        print(f"\n📁 输出文件:")
        print(f"  - Excel文件: {output_path}")
        if report_path:
            print(f"  - 报告文件: {report_path}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        log.error(f"主程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
