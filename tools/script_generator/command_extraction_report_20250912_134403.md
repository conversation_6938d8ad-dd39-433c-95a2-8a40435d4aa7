# 指令提取报告

## 生成时间
2025-09-12 13:44:03

## 统计信息
- **总记录数**: 120
- **成功提取**: 120 (100.0%)
- **部分提取**: 0
- **提取失败**: 0
- **跳过记录**: 0

## 输出文件
- **增强Excel文件**: `enhanced_query_data_20250912_134403.xlsx`
- **报告文件**: `command_extraction_report_20250912_134403.md`

## 提取详情

### 成功提取的记录
- **test_max_ring_volume**: `max ring volume`
- **test_adjustment_the_brightness_to_minimun**: `adjustment the brightness to minimun`
- **test_turn_off_auto_rotate_screen**: `turn off auto rotate screen`
- **test_disable_call_rejection**: `disable call rejection`
- **test_set_parallel_windows**: `set parallel windows`
- **test_turn_off_adaptive_brightness**: `turn off adaptive brightness`
- **test_turn_down_the_brightness_to_the_min**: `turn down the brightness to the min`
- **test_adjustment_the_brightness_to**: `Adjustment the brightness to 50%`
- **test_set_sim_ringtone**: `set sim1 ringtone`
- **test_switch_magic_voice_to_mango**: `switch magic voice to Mango`
- **test_turn_on_the_7am_alarm**: `turn on the 7AM alarm`
- **test_check_ram_information**: `check ram information`
- **test_set_edge_mistouch_prevention**: `set edge mistouch prevention`
- **test_set_split_screen_apps**: `set split-screen apps`
- **test_turn_off_driving_mode**: `turn off driving mode`
- **test_switch_to_davido_voice**: `Switch to davido voice`
- **test_increase_the_brightness**: `increase the brightness`
- **test_close_power_saving_mode**: `close power saving mode`
- **test_switch_to_flash_notification**: `switch to flash notification`
- **test_turn_on_auto_rotate_screen**: `turn on auto rotate screen`
- **test_switching_charging_speed**: `switching charging speed`
- **test_help_me_take_a_long_screenshot**: `help me take a long screenshot`
- **test_turn_on_high_brightness_mode**: `turn on high brightness mode`
- **test_turn_down_ring_volume**: `turn down ring volume`
- **test_smart_charge**: `smart charge`
- **test_set_screen_to_minimum_brightness**: `set screen to minimum brightness`
- **test_jump_to_battery_usage**: `jump to battery usage`
- **test_set_scheduled_power_on_off_and_restart**: `set scheduled power on/off and restart`
- **test_min_brightness**: `min brightness`
- **test_check_system_update**: `check system update`
- **test_turn_off_show_battery_percentage**: `turn off show battery percentage`
- **test_jump_to_notifications_and_status_bar_settings**: `jump to notifications and status bar settings`
- **test_turn_up_the_volume_to_the_max**: `turn up the volume to the max`
- **test_enable_network_enhancement**: `Enable Network Enhancement`
- **test_check_front_camera_information**: `check front camera information`
- **test_set_call_back_with_last_used_sim**: `set call back with last used sim`
- **test_open_camera**: `open camera`
- **test_open_flashlight**: `open flashlight`
- **test_close_equilibrium_mode**: `close equilibrium mode`
- **test_disable_running_lock**: `disable running lock`
- **test_set_my_themes**: `set my themes`
- **test_long_screenshot**: `long screenshot`
- **test_enable_running_lock**: `enable running lock`
- **test_turn_off_light_theme**: `turn off light theme`
- **test_help_me_take_a_screenshot**: `help me take a screenshot`
- **test_maximum_volume**: `maximum volume`
- **test_open_bt**: `open bt`
- **test_turn_up_ring_volume**: `turn up ring volume`
- **test_set_flip_case_feature**: `set flip case feature`
- **test_check_my_balance_of_sim**: `check my balance of sim1`
- **test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up**: `I think the screen is a bit dark now. Could you please help me brighten it up?`
- **test_set_phantom_v_pen**: `set phantom v pen`
- **test_the_battery_of_the_mobile_phone_is_too_low**: `the battery of the mobile phone is too low`
- **test_turn_on_brightness_to_80**: `turn on brightness to 80%`
- **test_minimum_volume**: `minimum volume`
- **test_turn_on_light_theme**: `turn on light theme`
- **test_set_ultra_power_saving**: `set ultra power saving`
- **test_min_ring_volume**: `min ring volume`
- **test_disable_auto_pickup**: `disable auto pickup`
- **test_turn_on_do_not_disturb_mode**: `turn on do not disturb mode`
- **test_close_performance_mode**: `close performance mode`
- **test_increase_settings_for_special_functions**: `increase settings for special functions`
- **test_min_alarm_clock_volume**: `min alarm clock volume`
- **test_switched_to_data_mode**: `switched to data mode`
- **test_jump_to_nfc_settings**: `jump to nfc settings`
- **test_increase_the_volume_to_the_maximun**: `increase the volume to the maximun`
- **test_set_customized_cover_screen**: `set customized cover screen`
- **test_turn_up_alarm_clock_volume**: `turn up alarm clock volume`
- **test_close_airplane**: `close airplane`
- **test_close_flashlight**: `close flashlight`
- **test_set_font_size**: `set font size`
- **test_set_app_auto_rotate**: `set app auto rotate`
- **test_set_date_time**: `set date & time`
- **test_set_smart_panel**: `set smart panel`
- **test_take_a_selfie**: `take a selfie`
- **test_switch_to_barrage_notification**: `Switch to Barrage Notification`
- **test_disable_magic_voice_changer**: `disable magic voice changer`
- **test_set_gesture_navigation**: `set gesture navigation`
- **test_turn_on_bluetooth**: `turn on bluetooth`
- **test_enable_all_ai_magic_box_features**: `enable all ai magic box features`
- **test_set_personal_hotspot**: `set personal hotspot`
- **test_set_cover_screen_apps**: `set cover screen apps`
- **test_set_screen_refresh_rate**: `set screen refresh rate`
- **test_set_battery_saver_settings**: `set battery saver settings`
- **test_set_off_a_firework**: `set off a firework`
- **test_set_notifications_volume_to**: `set notifications volume to 50%`
- **test_set_my_fonts**: `set my fonts`
- **test_jump_to_battery_and_power_saving**: `jump to battery and power saving`
- **test_jump_to_adaptive_brightness_settings**: `jump to adaptive brightness settings`
- **test_switch_to_smart_charge**: `switch to smart charge`
- **test_set_special_function**: `set special function`
- **test_set_color_style**: `set color style`
- **test_turn_off_nfc**: `turn off nfc`
- **test_set_lockscreen_passwords**: `set lockscreen passwords`
- **test_enable_zonetouch_master**: `enable zonetouch master`
- **test_set_screen_timeout**: `set screen timeout`
- **test_set_flex_still_mode**: `set flex-still mode`
- **test_open_the_settings**: `open the settings`
- **test_turn_on_light_theme**: `turn on light theme`
- **test_open_notification_ringtone_settings**: `open notification ringtone settings`
- **test_turn_on_smart_reminder**: `turn on smart reminder`
- **test_check_battery_information**: `check battery information`
- **test_more_settings**: `more settings`
- **test_switch_to_equilibrium_mode**: `switch to equilibrium mode`
- **test_open_settings**: `open settings`
- **test_set_nfc_tag**: `set nfc tag`
- **test_jump_to_ai_wallpaper_generator_settings**: `jump to ai wallpaper generator settings`
- **test_set_timezone**: `set timezone`
- **test_turn_on_location_services**: `turn on location services`
- **test_set_battery_saver_setting**: `set Battery Saver setting`
- **test_turn_on_wifi**: `turn on wifi`
- **test_enable_accelerate_dialogue**: `enable accelerate dialogue`
- **test_max_notifications_volume**: `max notifications volume`
- **test_turn_off_smart_reminder**: `turn off smart reminder`
- **test_switch_to_low_temp_charge**: `Switch to Low-Temp Charge`
- **test_enable_touch_optimization**: `enable touch optimization`
- **test_set_smart_hub**: `set smart hub`
- **test_check_mobile_data_balance_of_sim**: `check mobile data balance of sim2`
- **test_turn_on_nfc**: `turn on nfc`
- **test_decrease_the_brightness**: `decrease the brightness`


## 使用说明

1. **增强Excel文件**包含以下新增列：
   - `提取的command`: 从脚本中提取的指令
   - `提取的expected_text`: 从脚本中提取的期望文本
   - `脚本路径`: 对应的测试脚本文件路径
   - `提取状态`: 提取结果状态
   - `错误信息`: 提取失败时的错误信息

2. **提取状态说明**：
   - `success`: 成功提取command和expected_text
   - `partial`: 只提取到command或expected_text中的一个
   - `failed`: 未能提取到任何信息
   - `skipped`: 跳过处理（通常是数据不完整）

3. **后续处理建议**：
   - 对于失败的记录，可以手动检查脚本文件
   - 对于部分成功的记录，可以补充缺失的信息
   - 成功的记录可以直接用于后续的脚本生成
