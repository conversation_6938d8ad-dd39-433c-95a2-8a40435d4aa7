#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书Excel自动上传工具
自动遍历指定目录，将距离当前时间最近的Excel文件上传至指定飞书目录

Author: AI Assistant
Date: 2025-09-10
"""

import os
import sys
import glob
import json
import logging
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any

# 尝试导入飞书SDK，如果没有安装则使用requests作为备选
try:
    import lark_oapi as lark
    from lark_oapi.api.drive.v1 import *
    LARK_SDK_AVAILABLE = True
except ImportError:
    LARK_SDK_AVAILABLE = False
    print("警告: 未安装lark_oapi SDK，将使用requests作为备选方案")
    print("建议安装: pip install lark_oapi")


class ExcelFileScanner:
    """Excel文件扫描器，负责遍历目录并找到最新的Excel文件"""
    
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or self._setup_default_logger()
    
    def _setup_default_logger(self) -> logging.Logger:
        """设置默认日志记录器"""
        logger = logging.getLogger(self.__class__.__name__)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def scan_directory(self, directory: str) -> List[Dict[str, Any]]:
        """
        扫描目录中的所有Excel文件
        
        Args:
            directory: 要扫描的目录路径
            
        Returns:
            List[Dict]: 包含文件信息的列表，每个字典包含path, name, mtime等信息
        """
        if not os.path.exists(directory):
            self.logger.error(f"目录不存在: {directory}")
            return []
        
        excel_files = []
        patterns = ['*.xlsx', '*.xls']
        
        for pattern in patterns:
            search_pattern = os.path.join(directory, pattern)
            files = glob.glob(search_pattern)
            
            for file_path in files:
                try:
                    stat_info = os.stat(file_path)
                    file_info = {
                        'path': file_path,
                        'name': os.path.basename(file_path),
                        'size': stat_info.st_size,
                        'mtime': stat_info.st_mtime,
                        'mtime_str': datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    }
                    excel_files.append(file_info)
                    self.logger.debug(f"发现Excel文件: {file_info['name']} ({file_info['mtime_str']})")
                except OSError as e:
                    self.logger.warning(f"无法获取文件信息: {file_path}, 错误: {e}")
        
        # 按修改时间排序，最新的在前
        excel_files.sort(key=lambda x: x['mtime'], reverse=True)
        self.logger.info(f"在目录 {directory} 中找到 {len(excel_files)} 个Excel文件")
        
        return excel_files
    
    def get_latest_excel(self, directory: str) -> Optional[Dict[str, Any]]:
        """
        获取目录中最新的Excel文件
        
        Args:
            directory: 要扫描的目录路径
            
        Returns:
            Optional[Dict]: 最新文件的信息，如果没有找到则返回None
        """
        files = self.scan_directory(directory)
        if not files:
            self.logger.warning(f"目录 {directory} 中没有找到Excel文件")
            return None
        
        latest_file = files[0]
        self.logger.info(f"找到最新Excel文件: {latest_file['name']} ({latest_file['mtime_str']})")
        return latest_file


class FeishuUploader:
    """飞书文件上传器，负责将文件上传到飞书云盘"""

    def __init__(self, app_id: str, app_secret: str, logger: logging.Logger = None, use_sdk: bool = True):
        self.app_id = app_id
        self.app_secret = app_secret
        self.logger = logger or self._setup_default_logger()
        self._access_token = None
        self._token_expires_at = 0
        self.use_sdk = use_sdk and LARK_SDK_AVAILABLE

        # 初始化飞书SDK客户端
        if self.use_sdk:
            self.client = lark.Client.builder() \
                .app_id(self.app_id) \
                .app_secret(self.app_secret) \
                .log_level(lark.LogLevel.INFO) \
                .build()
            self.logger.info("使用飞书官方SDK进行文件上传")
        else:
            self.client = None
            self.logger.info("使用requests库进行文件上传")
    
    def _setup_default_logger(self) -> logging.Logger:
        """设置默认日志记录器"""
        logger = logging.getLogger(self.__class__.__name__)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def get_app_access_token(self) -> Tuple[str, Optional[Exception]]:
        """
        获取 app_access_token

        Returns:
            Tuple[str, Optional[Exception]]: (access_token, error)
        """
        # 检查token是否还有效（提前5分钟刷新）
        current_time = datetime.now().timestamp()
        if self._access_token and current_time < (self._token_expires_at - 300):
            return self._access_token, None

        url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        headers = {
            "Content-Type": "application/json; charset=utf-8"
        }

        try:
            self.logger.info("正在获取飞书应用访问令牌...")
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get("code", 0) != 0:
                error_msg = f"获取应用访问令牌失败: {result.get('msg', 'unknown error')}"
                self.logger.error(error_msg)
                return "", Exception(error_msg)

            self._access_token = result["app_access_token"]
            # 令牌有效期通常是2小时，这里记录过期时间
            self._token_expires_at = current_time + result.get("expire", 7200)

            self.logger.info("成功获取飞书应用访问令牌")
            return self._access_token, None

        except Exception as e:
            error_msg = f"获取应用访问令牌异常: {e}"
            self.logger.error(error_msg)
            return "", e
    
    def upload_file_to_folder(self, file_path: str, folder_token: str) -> Optional[str]:
        """
        将本地文件上传至飞书指定文件夹

        Args:
            file_path: 本地文件路径
            folder_token: 目标文件夹 token

        Returns:
            Optional[str]: 上传成功后返回 file_token，否则返回 None
        """
        # 检查文件
        if not os.path.isfile(file_path):
            self.logger.error(f"文件不存在: {file_path}")
            return None

        file_size = os.path.getsize(file_path)
        if file_size == 0:
            self.logger.error(f"不可上传空文件: {file_path}")
            return None

        file_name = os.path.basename(file_path)
        self.logger.info(f"准备上传文件: {file_name}, 大小: {file_size} 字节")

        # 根据是否有SDK选择上传方式
        if self.use_sdk:
            return self._upload_with_sdk(file_path, folder_token, file_size)
        else:
            # 获取访问令牌
            access_token, error = self.get_app_access_token()
            if error:
                self.logger.error(f"无法获取访问令牌: {error}")
                return None

            # 20MB以内直接上传
            if file_size <= 20 * 1024 * 1024:
                return self._upload_small_file(access_token, file_path, folder_token, file_size)
            else:
                # 分片上传
                self.logger.info("文件大于20MB，采用分片上传")
                return self._upload_large_file(access_token, file_path, folder_token)
    
    def _upload_small_file(self, access_token: str, file_path: str, 
                          folder_token: str, file_size: int) -> Optional[str]:
        """上传小文件（<=20MB）"""
        url = "https://open.feishu.cn/open-apis/drive/v1/files/upload_all"
        
        try:
            with open(file_path, "rb") as f:
                files = {
                    "file": (os.path.basename(file_path), f, "application/octet-stream")
                }
                data = {
                    "file_name": os.path.basename(file_path),
                    "parent_type": "explorer",
                    "parent_node": folder_token,
                    "size": str(file_size)
                }
                headers = {
                    "Authorization": f"Bearer {access_token}"
                }
                
                self.logger.info("正在上传文件...")
                response = requests.post(url, headers=headers, data=data, files=files, timeout=300)
                
                try:
                    result = response.json()
                except Exception:
                    self.logger.error(f"非JSON响应: {response.text}")
                    return None
                
                if result.get("code", 0) != 0:
                    self.logger.error(f"上传失败: {result.get('msg', 'unknown error')}")
                    return None
                
                file_token = result["data"]["file_token"]
                self.logger.info(f"文件上传成功, file_token: {file_token}")
                return file_token
                
        except Exception as e:
            self.logger.error(f"上传文件异常: {e}")
            return None
    
    def _upload_with_sdk(self, file_path: str, folder_token: str, file_size: int) -> Optional[str]:
        """使用飞书SDK上传文件"""
        try:
            file_name = os.path.basename(file_path)

            # 构造请求对象
            with open(file_path, "rb") as file:
                request: UploadAllFileRequest = UploadAllFileRequest.builder() \
                    .request_body(UploadAllFileRequestBody.builder()
                        .file_name(file_name)
                        .parent_type("explorer")
                        .parent_node(folder_token)
                        .size(str(file_size))
                        .file(file)
                        .build()) \
                    .build()

                self.logger.info("正在使用SDK上传文件...")
                user_access_token = self.get_app_access_token()[0]
                # 发起请求
                option = lark.RequestOption.builder().user_access_token(
                    user_access_token).build()
                response: UploadAllFileResponse = self.client.drive.v1.file.upload_all(request, option)

                # 处理失败返回
                if not response.success():
                    error_msg = f"SDK上传失败, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                    self.logger.error(error_msg)

                    # 如果是权限问题，提供更详细的错误信息
                    if response.code == 403:
                        self.logger.error("权限不足，请检查应用是否有云空间权限")
                    elif response.code == 400:
                        self.logger.error("请求参数错误，请检查文件夹token是否正确")

                    return None

                # 处理业务结果
                file_token = response.data.file_token
                self.logger.info(f"SDK文件上传成功, file_token: {file_token}")
                return file_token

        except Exception as e:
            self.logger.error(f"SDK上传文件异常: {e}")
            # 如果SDK上传失败，尝试使用requests备选方案
            self.logger.info("尝试使用requests备选方案...")
            access_token, error = self.get_app_access_token()
            if error:
                self.logger.error(f"无法获取访问令牌: {error}")
                return None
            return self._upload_small_file(access_token, file_path, folder_token, file_size)

    def _upload_large_file(self, access_token: str, file_path: str, folder_token: str) -> Optional[str]:
        """上传大文件（>20MB）- 分片上传"""
        # 这里可以实现分片上传逻辑，暂时简化处理
        self.logger.warning("大文件分片上传功能待实现，请确保文件小于20MB")
        return None


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or os.path.join(
            os.path.dirname(__file__), 'excel_uploader_config.json'
        )
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "app_id": "cli_APP_ID",
            "app_secret": "APP_SECRET",
            "directories": {
                "allure_report_excel": {
                    "local_path": "tools/allure_report_analysis/allure_report_excel",
                    "feishu_folder_token": "IwYcfFkFqlY4hHdOcxKcVsvdnoh",
                    "description": "挂测结果目录"
                },
                "failure_analysis": {
                    "local_path": "tools/allure_report_analysis/failure_analysis",
                    "feishu_folder_token": "BrmHfSRPClwbvDdsEMpcoSTvnGf",
                    "description": "挂测结果分析目录"
                }
            },
            "log_level": "INFO",
            "retry_times": 3,
            "retry_delay": 5,
            "use_sdk": True,
            "upload_timeout": 300
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
        
        # 创建默认配置文件
        self._save_config(default_config)
        return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def get_directories(self) -> Dict[str, Dict[str, str]]:
        """获取目录配置"""
        return self.config.get("directories", {})


class ExcelAutoUploader:
    """Excel自动上传主程序"""

    def __init__(self, config_file: str = None, use_sdk: bool = None):
        self.config_manager = ConfigManager(config_file)
        self.logger = self._setup_logger()

        # 初始化组件
        app_id = self.config_manager.get("app_id")
        app_secret = self.config_manager.get("app_secret")

        if not app_id or not app_secret or app_id == "cli_APP_ID":
            self.logger.error("请在配置文件中设置正确的app_id和app_secret")
            raise ValueError("飞书应用配置不完整")

        # 如果没有指定use_sdk，则从配置文件读取
        if use_sdk is None:
            use_sdk = self.config_manager.get("use_sdk", True)

        self.scanner = ExcelFileScanner(self.logger)
        self.uploader = FeishuUploader(app_id, app_secret, self.logger, use_sdk)

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("ExcelAutoUploader")
        log_level = self.config_manager.get("log_level", "INFO")
        logger.setLevel(getattr(logging, log_level.upper()))

        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)

            # 文件输出
            log_dir = os.path.join(os.path.dirname(__file__), 'logs')
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, f'excel_uploader_{datetime.now().strftime("%Y%m%d")}.log')

            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)

        return logger

    def upload_latest_excel_from_directory(self, directory_name: str) -> bool:
        """
        从指定目录上传最新的Excel文件

        Args:
            directory_name: 目录配置名称

        Returns:
            bool: 上传是否成功
        """
        directories = self.config_manager.get_directories()
        if directory_name not in directories:
            self.logger.error(f"未找到目录配置: {directory_name}")
            return False

        dir_config = directories[directory_name]
        local_path = dir_config["local_path"]
        folder_token = dir_config["feishu_folder_token"]
        description = dir_config.get("description", directory_name)

        self.logger.info(f"开始处理{description}: {local_path}")

        # 转换为绝对路径
        if not os.path.isabs(local_path):
            # 相对于项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            local_path = os.path.join(project_root, local_path)

        # 查找最新Excel文件
        latest_file = self.scanner.get_latest_excel(local_path)
        if not latest_file:
            self.logger.warning(f"{description}中没有找到Excel文件")
            return False

        # 上传文件
        retry_times = self.config_manager.get("retry_times", 3)
        retry_delay = self.config_manager.get("retry_delay", 5)

        for attempt in range(retry_times):
            try:
                self.logger.info(f"第{attempt + 1}次尝试上传文件: {latest_file['name']}")
                file_token = self.uploader.upload_file_to_folder(
                    latest_file['path'], folder_token
                )

                if file_token:
                    self.logger.info(f"[SUCCESS] {description}文件上传成功: {latest_file['name']}")
                    return True
                else:
                    self.logger.warning(f"第{attempt + 1}次上传失败")

            except Exception as e:
                self.logger.error(f"第{attempt + 1}次上传异常: {e}")

            if attempt < retry_times - 1:
                self.logger.info(f"等待{retry_delay}秒后重试...")
                import time
                time.sleep(retry_delay)

        self.logger.error(f"[ERROR] {description}文件上传失败，已重试{retry_times}次")
        return False

    def upload_all_latest_excel(self) -> Dict[str, bool]:
        """
        上传所有配置目录中的最新Excel文件

        Returns:
            Dict[str, bool]: 每个目录的上传结果
        """
        directories = self.config_manager.get_directories()
        results = {}

        self.logger.info("=" * 60)
        self.logger.info("开始执行Excel文件自动上传任务")
        self.logger.info("=" * 60)

        for directory_name in directories.keys():
            try:
                success = self.upload_latest_excel_from_directory(directory_name)
                results[directory_name] = success
            except Exception as e:
                self.logger.error(f"处理目录 {directory_name} 时发生异常: {e}")
                results[directory_name] = False

        # 输出总结
        self.logger.info("=" * 60)
        self.logger.info("上传任务完成，结果汇总:")
        success_count = 0
        for directory_name, success in results.items():
            dir_config = directories.get(directory_name, {})
            description = dir_config.get("description", directory_name)
            status = "[SUCCESS] 成功" if success else "[ERROR] 失败"
            self.logger.info(f"  {description}: {status}")
            if success:
                success_count += 1

        self.logger.info(f"总计: {success_count}/{len(results)} 个目录上传成功")
        self.logger.info("=" * 60)

        return results


def main():
    """主程序入口"""
    try:
        # 创建上传器实例
        uploader = ExcelAutoUploader()

        # 执行上传任务
        results = uploader.upload_all_latest_excel()

        # 根据结果设置退出码
        success_count = sum(1 for success in results.values() if success)
        if success_count == len(results):
            print("所有文件上传成功！")
            sys.exit(0)
        elif success_count > 0:
            print("部分文件上传成功")
            sys.exit(1)
        else:
            print("所有文件上传失败")
            sys.exit(2)

    except Exception as e:
        print(f"程序执行异常: {e}")
        sys.exit(3)


if __name__ == "__main__":
    main()
    # feishu_uploader =FeishuUploader(app_id="cli_a8821ea37977d01c", app_secret="YLuPbaYFE8MOaYUE5pyccdlywJtqkRPi")
    # print(feishu_uploader.get_app_access_token())
