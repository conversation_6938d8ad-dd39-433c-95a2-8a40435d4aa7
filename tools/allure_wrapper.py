#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Allure命令包装器
自动使用正确的Allure路径和JAVA_HOME配置
"""

import subprocess
import sys
import os

# 配置常量
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ALLURE_PATH = os.path.abspath(os.path.join(ROOT_DIR, "utils/allure-2.34.0/bin/allure.bat"))
# JAVA_HOME = r"D:\app_test\utils\JDK"
JAVA_HOME = os.path.abspath(os.path.join(ROOT_DIR, "utils/JDK"))

def validate_paths():
    """验证必要的路径是否存在"""
    errors = []

    # 检查Allure路径
    if not os.path.exists(ALLURE_PATH):
        errors.append(f"Allure路径不存在: {ALLURE_PATH}")

    # 检查JAVA_HOME路径
    if not os.path.exists(JAVA_HOME):
        errors.append(f"JAVA_HOME路径不存在: {JAVA_HOME}")

    # 检查java.exe是否存在
    java_exe = os.path.join(JAVA_HOME, "bin", "java.exe")
    if not os.path.exists(java_exe):
        errors.append(f"Java可执行文件不存在: {java_exe}")

    return errors

def setup_environment():
    """设置环境变量"""
    # 设置JAVA_HOME环境变量
    os.environ["JAVA_HOME"] = JAVA_HOME

    # 将Java bin目录添加到PATH前面，确保使用指定的Java版本
    java_bin = os.path.join(JAVA_HOME, "bin")
    current_path = os.environ.get("PATH", "")
    os.environ["PATH"] = f"{java_bin};{current_path}"

    print(f"设置JAVA_HOME: {JAVA_HOME}")
    print(f"Java版本路径: {java_bin}")

def main():
    """主函数"""
    try:
        # 验证路径
        errors = validate_paths()
        if errors:
            for error in errors:
                print(f"错误: {error}")
            sys.exit(1)

        # 设置环境变量
        setup_environment()

        # 构建命令
        cmd = [ALLURE_PATH] + sys.argv[1:]
        print(f"执行命令: {' '.join(cmd)}")

        # 执行命令，传递修改后的环境变量
        result = subprocess.run(
            cmd,
            cwd=".",
            text=True,
            capture_output=False,
            env=os.environ.copy()
        )
        sys.exit(result.returncode)

    except Exception as e:
        print(f"执行Allure命令失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
